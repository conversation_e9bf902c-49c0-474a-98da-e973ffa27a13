# Email Templates

This directory contains all transactional email templates for the Imoblr platform.

## Structure

Each email template should be organized in its own subdirectory with the following files:

```
email-templates/
├── template-name/
│   ├── template.html      # HTML version of the email
│   ├── template.txt       # Plain text version (optional)
│   ├── subject.txt        # Email subject line
│   └── variables.md       # Documentation of available variables
└── README.md
```

## Template Variables

Templates use the `{{variable}}` syntax for variable substitution. Common variables include:

- `{{recipientName}}` - Name of the email recipient
- `{{senderName}}` - Name of the person/system sending the email
- `{{companyName}}` - Company/platform name
- `{{actionUrl}}` - URL for primary call-to-action
- `{{supportEmail}}` - Support contact email

## Available Templates

- **team-invite** - Invitation to join a team
- More templates will be added as needed

## Usage

Templates are loaded and processed by the EmailService class in `services/email.ts`. The service handles variable substitution and email sending via Resend.

Example:
```typescript
const emailService = createEmailService(env);
const template = await emailService.loadTemplate('team-invite', {
  recipientName: '<PERSON>',
  teamName: 'Acme Corp',
  inviterName: '<PERSON>',
  inviteUrl: 'https://app.imoblr.com/invite/abc123'
});

await emailService.sendEmail(template, {
  to: '<EMAIL>'
});
```
