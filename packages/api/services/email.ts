import { Resend } from 'resend';
import type { Env } from '../types';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface SendEmailOptions {
  to: string | string[];
  from?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  idempotencyKey?: string;
}

export interface EmailServiceResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Reusable email service for sending transactional emails via Resend
 */
export class EmailService {
  private resend: Resend;
  private defaultFromAddress: string;

  constructor(apiKey: string, defaultFromAddress: string = 'Imoblr <<EMAIL>>') {
    this.resend = new Resend(apiKey);
    this.defaultFromAddress = defaultFromAddress;
  }

  /**
   * Create EmailService instance from environment
   */
  static fromEnv(env: Env): EmailService {
    if (!env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY environment variable is required');
    }
    return new EmailService(env.RESEND_API_KEY);
  }

  /**
   * Send an email using a template
   */
  async sendEmail(
    template: EmailTemplate,
    options: SendEmailOptions
  ): Promise<EmailServiceResult> {
    try {
      const emailData = {
        from: options.from || this.defaultFromAddress,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: template.subject,
        html: template.html,
        text: template.text,
        cc: options.cc ? (Array.isArray(options.cc) ? options.cc : [options.cc]) : undefined,
        bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc : [options.bcc]) : undefined,
        reply_to: options.replyTo,
      };

      const sendOptions = options.idempotencyKey 
        ? { idempotencyKey: options.idempotencyKey }
        : undefined;

      const { data, error } = await this.resend.emails.send(emailData, sendOptions);

      if (error) {
        console.error('Resend API error:', error);
        return {
          success: false,
          error: error.message || 'Failed to send email'
        };
      }

      return {
        success: true,
        messageId: data?.id
      };
    } catch (error) {
      console.error('Email service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Load and process an HTML email template with variable substitution
   */
  async loadTemplate(
    templatePath: string,
    variables: Record<string, string>
  ): Promise<EmailTemplate> {
    try {
      // In Cloudflare Workers, we'll need to import the template content
      // For now, we'll return a basic structure that can be extended
      const templateContent = await this.getTemplateContent(templatePath);
      
      // Replace variables in the template
      let processedHtml = templateContent.html;
      let processedSubject = templateContent.subject;
      let processedText = templateContent.text;

      // Simple variable substitution using {{variable}} syntax
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        processedHtml = processedHtml.replace(new RegExp(placeholder, 'g'), value);
        processedSubject = processedSubject.replace(new RegExp(placeholder, 'g'), value);
        if (processedText) {
          processedText = processedText.replace(new RegExp(placeholder, 'g'), value);
        }
      });

      return {
        subject: processedSubject,
        html: processedHtml,
        text: processedText
      };
    } catch (error) {
      console.error('Template loading error:', error);
      throw new Error(`Failed to load email template: ${templatePath}`);
    }
  }

  /**
   * Get template content - this will be implemented to load from the templates directory
   */
  private async getTemplateContent(templatePath: string): Promise<EmailTemplate> {
    // This is a placeholder - in a real implementation, you would load the template
    // from the file system or embed it in the bundle
    throw new Error(`Template loading not yet implemented for: ${templatePath}`);
  }
}

/**
 * Utility function to create email service instance
 */
export function createEmailService(env: Env): EmailService {
  return EmailService.fromEnv(env);
}
