import { Resend } from 'resend';
import type { Env } from '../types';

export interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

export interface SendEmailOptions {
  to: string | string[];
  from?: string;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
  idempotencyKey?: string;
}

export interface EmailServiceResult {
  success: boolean;
  messageId?: string;
  error?: string;
}

/**
 * Reusable email service for sending transactional emails via Resend
 */
export class EmailService {
  private resend: Resend;
  private defaultFromAddress: string;

  constructor(apiKey: string, defaultFromAddress: string = 'Imoblr <<EMAIL>>') {
    this.resend = new Resend(apiKey);
    this.defaultFromAddress = defaultFromAddress;
  }

  /**
   * Create EmailService instance from environment
   */
  static fromEnv(env: Env): EmailService {
    if (!env.RESEND_API_KEY) {
      throw new Error('RESEND_API_KEY environment variable is required');
    }
    return new EmailService(env.RESEND_API_KEY);
  }

  /**
   * Send an email using a template
   */
  async sendEmail(
    template: EmailTemplate,
    options: SendEmailOptions
  ): Promise<EmailServiceResult> {
    try {
      const emailData = {
        from: options.from || this.defaultFromAddress,
        to: Array.isArray(options.to) ? options.to : [options.to],
        subject: template.subject,
        html: template.html,
        text: template.text,
        cc: options.cc ? (Array.isArray(options.cc) ? options.cc : [options.cc]) : undefined,
        bcc: options.bcc ? (Array.isArray(options.bcc) ? options.bcc : [options.bcc]) : undefined,
        reply_to: options.replyTo,
      };

      const sendOptions = options.idempotencyKey 
        ? { idempotencyKey: options.idempotencyKey }
        : undefined;

      const { data, error } = await this.resend.emails.send(emailData, sendOptions);

      if (error) {
        console.error('Resend API error:', error);
        return {
          success: false,
          error: error.message || 'Failed to send email'
        };
      }

      return {
        success: true,
        messageId: data?.id
      };
    } catch (error) {
      console.error('Email service error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  /**
   * Load and process an HTML email template with variable substitution
   */
  async loadTemplate(
    templatePath: string,
    variables: Record<string, string>
  ): Promise<EmailTemplate> {
    try {
      // In Cloudflare Workers, we'll need to import the template content
      // For now, we'll return a basic structure that can be extended
      const templateContent = await this.getTemplateContent(templatePath);
      
      // Replace variables in the template
      let processedHtml = templateContent.html;
      let processedSubject = templateContent.subject;
      let processedText = templateContent.text;

      // Simple variable substitution using {{variable}} syntax
      Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        processedHtml = processedHtml.replace(new RegExp(placeholder, 'g'), value);
        processedSubject = processedSubject.replace(new RegExp(placeholder, 'g'), value);
        if (processedText) {
          processedText = processedText.replace(new RegExp(placeholder, 'g'), value);
        }
      });

      return {
        subject: processedSubject,
        html: processedHtml,
        text: processedText
      };
    } catch (error) {
      console.error('Template loading error:', error);
      throw new Error(`Failed to load email template: ${templatePath}`);
    }
  }

  /**
   * Get template content - loads from embedded templates
   */
  private async getTemplateContent(templatePath: string): Promise<EmailTemplate> {
    // For now, we'll handle the team-invite template directly
    // In the future, this could be extended to load from a template registry
    if (templatePath === 'team-invite') {
      return await this.getTeamInviteTemplate();
    }

    throw new Error(`Template not found: ${templatePath}`);
  }

  /**
   * Get the team invite template content
   */
  private async getTeamInviteTemplate(): Promise<EmailTemplate> {
    const subject = `You're invited to join {{teamName}} on Imoblr`;

    const html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Team Invitation - Imoblr</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 10px;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }
        .content {
            margin-bottom: 30px;
        }
        .team-info {
            background-color: #f3f4f6;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #2563eb;
        }
        .team-name {
            font-size: 18px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        .inviter {
            color: #6b7280;
            font-size: 14px;
        }
        .cta-button {
            display: inline-block;
            background-color: #2563eb;
            color: #ffffff;
            text-decoration: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
            transition: background-color 0.2s;
        }
        .cta-button:hover {
            background-color: #1d4ed8;
        }
        .cta-container {
            text-align: center;
            margin: 30px 0;
        }
        .expiry-notice {
            background-color: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            font-size: 14px;
            color: #92400e;
        }
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            font-size: 14px;
            color: #6b7280;
            text-align: center;
        }
        .footer a {
            color: #2563eb;
            text-decoration: none;
        }
        .alternative-link {
            margin-top: 20px;
            padding: 15px;
            background-color: #f9fafb;
            border-radius: 6px;
            font-size: 14px;
            color: #6b7280;
            word-break: break-all;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .container {
                padding: 20px;
            }
            .title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">Imoblr</div>
        </div>

        <h1 class="title">You're invited to join a team!</h1>

        <div class="content">
            <p>Hello {{recipientName}},</p>

            <p>{{inviterName}} has invited you to join their team on Imoblr, a powerful platform for real estate professionals.</p>

            <div class="team-info">
                <div class="team-name">{{teamName}}</div>
                <div class="inviter">Invited by {{inviterName}}</div>
            </div>

            <p>By joining this team, you'll be able to:</p>
            <ul>
                <li>Collaborate on real estate projects</li>
                <li>Share property listings and resources</li>
                <li>Access team-specific tools and features</li>
                <li>Work together more efficiently</li>
            </ul>
        </div>

        <div class="cta-container">
            <a href="{{inviteUrl}}" class="cta-button">Accept Invitation</a>
        </div>

        <div class="expiry-notice">
            <strong>⏰ Time-sensitive:</strong> This invitation will expire on {{expiryDate}}. Please accept it soon to join the team.
        </div>

        <div class="alternative-link">
            <strong>Having trouble with the button?</strong><br>
            Copy and paste this link into your browser:<br>
            {{inviteUrl}}
        </div>

        <div class="footer">
            <p>If you weren't expecting this invitation or believe it was sent in error, you can safely ignore this email.</p>
            <p>
                Need help? Contact our support team at
                <a href="mailto:{{supportEmail}}">{{supportEmail}}</a>
            </p>
            <p>
                <a href="{{unsubscribeUrl}}">Unsubscribe</a> from team invitations
            </p>
        </div>
    </div>
</body>
</html>`;

    const text = `IMOBLR - TEAM INVITATION

You're invited to join {{teamName}}!

Hello {{recipientName}},

{{inviterName}} has invited you to join their team on Imoblr, a powerful platform for real estate professionals.

TEAM DETAILS:
- Team Name: {{teamName}}
- Invited by: {{inviterName}}

By joining this team, you'll be able to:
• Collaborate on real estate projects
• Share property listings and resources
• Access team-specific tools and features
• Work together more efficiently

ACCEPT YOUR INVITATION:
{{inviteUrl}}

⏰ IMPORTANT: This invitation will expire on {{expiryDate}}. Please accept it soon to join the team.

---

If you weren't expecting this invitation or believe it was sent in error, you can safely ignore this email.

Need help? Contact our support team at {{supportEmail}}

To unsubscribe from team invitations: {{unsubscribeUrl}}

© Imoblr - Real Estate Platform`;

    return {
      subject,
      html,
      text
    };
  }
}

/**
 * Utility function to create email service instance
 */
export function createEmailService(env: Env): EmailService {
  return EmailService.fromEnv(env);
}
